{"name": "goofish-product-publisher", "version": "1.0.0", "description": "闲鱼商品自动发布脚本", "main": "goofish-product-publisher.js", "scripts": {"start": "node goofish-product-publisher.js", "install-playwright": "npx playwright install chromium", "example": "node example-usage.js", "example:batch": "node example-usage.js batch", "config:shared": "node browser-config-examples.js shared", "config:headless": "node browser-config-examples.js headless", "config:temp": "node browser-config-examples.js temp", "config:help": "node browser-config-examples.js --help"}, "keywords": ["goofish", "automation", "playwright", "product-publisher"], "author": "Your Name", "license": "MIT", "dependencies": {"playwright": "^1.40.0"}, "engines": {"node": ">=16.0.0"}}